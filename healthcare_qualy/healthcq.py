import pandas as pd
import logging
import io
import os
from sqlalchemy import create_engine, Table, Column, Integer, String, MetaData
from sqlalchemy.sql import text
from dotenv import load_dotenv

logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
    )

# Load environmental variables from a .env file
load_dotenv()
DB_USER = os.getenv("DB_USER")
DB_PASS = os.getenv("DB_PASS")
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT", "25060")
DB_NAME = os.getenv("DB_NAME", "defaultdb")

results = []
bank_balances = {}

quota = 120
bank_cap =720


# psql connection function
def psql_engine():
    try:
        pg_engine = create_engine(f'postgresql+psycopg2://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}',
                isolation_level="AUTOCOMMIT"  # This enables immediate execution of DDL statements
                )
        psql_conn = pg_engine.connect()
        logging.info("psql connection worked")
        return psql_conn
    except Exception as e:
        logging.error(f"error during psql connection process: {e}")
        return None

# function for reading file
def read_sql_files(file_path):
    try:
        with open(file_path, "r") as file:
            sql_script = file.read()
        return sql_script
    except Exception as e:
        logging.error(f"Error reading sql file {file_path}: e")

# function to create df from sql file
def create_df():
    psql_conn = psql_engine()
    if psql_conn is None:
        logging.error("PSQL connection fail, exiting script.")
        return None
    try:
        # read sql to dataframe
        hcsql = read_sql_files("/mnt/c/users/rflores/poetry_env/KP_EthnicityGender/healthcare_qualy/hcqualified.sql")
        df = pd.read_sql_query(hcsql, psql_conn)
        logging.info("ran sql script. Yay!")
        return df
    finally:
        psql_conn.close()
    print(df.head())

# clean dataframe and format
def main():
    dataframe = create_df()
    if dataframe is None:
        logging.error("main() does not have a working df to use.")
        return None

    monthly_hours = dataframe.groupby(['ubcid', 'member_name', 'trust_local', 'work_period'])['hours'].sum().reset_index()
    monthly_hours = monthly_hours.sort_values(by=['ubcid', 'work_period'])
    if monthly_hours is None:
        logging.error("main() does not have a working df to use.")
        return None
    

    # process each row. Why are we processing?
    for _, row in monthly_hours.iterrows():
        memberid   = row['ubcid']
        membername = row['member_name']
        trustlocal = row['trust_local']
        workperiod = row['work_period']
        hours      = row['hours']
        
        # initialize bank
        if memberid not in bank_balances:
            bank_balances[memberid] = 0
         
        bank           = bank_balances[memberid]
        quota_met      = False
        used_from_bank = 0
        added_to_bank  = 0

        if hours >= quota:
            quota_met     = True
            overage       = hours - quota
            new_bank      = min(bank + overage, bank_cap)
            added_to_bank = new_bank - bank
            bank          = new_bank
        else:
            shortfall = quota - hours
            if bank >= shortfall:
                quota_met      = True
                used_from_bank = shortfall
                bank          -= shortfall
            else:
                quota_met      = False
                used_from_bank = bank
                bank = 0

        # save updated bank
        bank_balances[memberid] = bank
        
        results.append({
            'ubcid'      :memberid,
            'member_name':membername,
            'trust_local':trustlocal,
            'work_period':workperiod,
            'hours'      :hours,
            'used_from_bank':used_from_bank,
            'added_to_bank' :added_to_bank,
            'bank_balance'  :bank,
            'quota_met'     :quota_met,
        })

    # Final Result Dataframe
    result_df = pd.DataFrame(results)

    print(result_df)
    result_df.to_csv("monthly_quota_results.csv", index=False)

if __name__ == "__main__":
    main()
