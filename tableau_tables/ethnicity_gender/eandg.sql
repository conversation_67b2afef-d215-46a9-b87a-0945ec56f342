DROP TABLE IF EXISTS kpEthnicityAnalysis;
CREATE TABLE kpEthnicityAnalysis AS
WITH membase AS (
SELECT
                     zzmember_base.first_name::text || ' ' || zzmember_base.last_name::text as name,
                     zzmember_base.local AS member_local,
                     zzmember_base.ubcid,
                     zzmember_base.status,
                     zzmember_base.ethnicity,
                     zzmember_base.gender,
                     zzmember_base.craft,
                     CASE
                        WHEN dob BETWEEN '1965-01-01' AND '1980-12-31' THEN 'Gen X'
                        WHEN dob BETWEEN '1981-01-01' AND '1996-12-31' THEN 'Millennial'
                        WHEN dob BETWEEN '1997-01-01' AND '2012-12-31' THEN 'Gen Z'
                        WHEN dob >= '2013-01-01' THEN 'Gen Alpha'
                        ELSE 'Baby Boomer or Older'
                     END AS genererations,
                     zzmember_base.ssn,
                      COUNT(CASE WHEN status IN ('GS', 'AR') AND UBCID IS NOT NULL THEN ubcid END)
                        OVER (PARTITION BY ubcid) AS membasecount
                 FROM zzmember_base
                    group by name, member_local, ubcid, status, ethnicity, gender, craft, dob, ssn

                ),
    csac AS (SELECT
                     csac_all_funds2.trust_local,
                     csac_all_funds2.member_id,
                     csac_all_funds2.hours,
                     work_period,
                     COUNT( CASE WHEN hours is not null AND work_period =TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'),
                                                                                 'YYYYMM')::INTEGER then member_id END)
                     OVER (PARTITION BY member_id) as trustcount
                 FROM csac_all_funds2
                 WHERE fund = 'DUES'::text
                 AND work_period BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '15 MONTH'), 'YYYYMM')::INTEGER
                         AND TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'), 'YYYYMM')::INTEGER
                 AND trust_local in ('1977','661', '503', '213', '323', '206')
                 group by csac_all_funds2.trust_local, csac_all_funds2.member_id, csac_all_funds2.hours, work_period

                  )
SELECT
       ubcid,
       member_id,
       name,
       status,
       member_local,
       trust_local,
       ethnicity,
       genererations,
       gender,
        craft,
        hours,
        work_period,
        trustcount,
        membasecount


FROM membase
FULL JOIN csac
    ON membase.ssn::text = csac.member_id::text
order by ubcid desc, trust_local desc;
