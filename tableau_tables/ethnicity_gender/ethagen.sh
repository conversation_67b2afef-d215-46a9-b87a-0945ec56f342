#!/bin/bash

# Exit script immediatly on error
set -e

LOGFILE="$(dirname "$0")/afamcron.log"
exec > >(tee -a "$LOGFILE") 2>&1

# print timestamp
echo "----- <PERSON><PERSON><PERSON> started at $(date) -----"

# Change to the script directory
cd /mnt/c/users/rflores/poetry_env/KP_EthnicityGender/tableau_tables/ethnicity_gender

poetry run python3 ethandg.py

# Print success message
echo "Sc<PERSON>t completed successfully at $(date)"
