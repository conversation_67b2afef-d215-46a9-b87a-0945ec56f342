CREATE TABLE kp_unemployment AS
WITH membase AS (SELECT

                     zzmember_base.local AS member_local,
                     zzmember_base.gender,
                     zzmember_base.ethnicity,
                     zzmember_base.ssn,
                     zzmember_base.ubcid,
                     zzmember_base.status
                 FROM zzmember_base
                 WHERE zzmember_base.status IN ('GS', 'AR')
                 AND zzmember_base.ubcid IS NOT NULL
                 --AND class NOT IN ('50DISABLED', 'RETIRED', 'EARLYRET', 'DISABLED', '50DISABLED')
                 GROUP BY zzmember_base.local, zzmember_base.gender, zzmember_base.ethnicity, zzmember_base.ssn, zzmember_base.ubcid, zzmember_base.status
                 --AND gender in ('M', 'F')
                 --AND local in ('1977', '661', '503', '213', '323', '206')
                ),
    csac AS (SELECT DISTINCT
                     csac_all_funds2.trust_local,
                     csac_all_funds2.member_id
                 FROM csac_all_funds2
                 WHERE fund = 'DUES'::text
                 AND work_period BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '15 MONTH'), 'YYYYMM')::INTEGER
                         AND TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'), 'YYYYMM')::INTEGER
                 --AND trust_local in ('1977', '661', '503', '213', '323', '206')
                 AND hours > 0
                 AND hours IS NOT NULL
                 AND member_id IS NOT NULL
                 group by csac_all_funds2.trust_local, csac_all_funds2.member_id, csac_all_funds2.hours
                  )
SELECT DISTINCT
      ubcid,
       member_local,
       membase.gender,
       membase.ethnicity,
       CASE WHEN member_id IS NOT NULL THEN 1 ELSE 0 END AS Employed,
           CASE WHEN ubcid IS NOT NULL THEN 1 ELSE 0 END AS Unionmembers


FROM membase
 FULL OUTER JOIN csac
    ON membase.ssn::text = csac.member_id::text

order by member_local desc;
