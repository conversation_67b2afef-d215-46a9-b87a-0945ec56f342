#!/bin/bash

# Exit script immediatly on error
set -e

LOGFILE="$(dirname "$0")/unemploycron.log"
exec > >(tee -a "$LOGFILE") 2>&1

# print timestamp
echo "----- <PERSON><PERSON><PERSON> started at $(date) -----"

# Change to the script directory
cd /mnt/c/users/rflores/poetry_env/KP_EthnicityGender/tableau_tables/unemployment

poetry run python3 unemployment.py

# Print success message
echo "Script completed successfully at $(date)"
