import pandas as pd
import logging
import io
import os
from sqlalchemy import create_engine, Table, Column, Integer, String, MetaData
from sqlalchemy.sql import text
from dotenv import load_dotenv

logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
    )

# Load environmental variables from a .env file
load_dotenv()
DB_USER = os.getenv("DB_USER")
DB_PASS = os.getenv("DB_PASS")
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT", "25060")
DB_NAME = os.getenv("DB_NAME", "defaultdb")

# psql connection function
def psql_engine(): ####TESTED: PASS####
    try:
        pg_engine = create_engine(f'postgresql+psycopg2://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}',
                isolation_level="AUTOCOMMIT"  # This enables immediate execution of DDL statements
                )
        psql_conn = pg_engine.connect()
        logging.info("psql connection worked")
        return psql_conn
    except Exception as e:
        logging.error(f"error during psql connection process: {e}")
        return None

#function for readin file
def read_sql_files(file_path):
    try:
        logging.info(f"Reading SQL file: {file_path}")
        with open(file_path, "r") as file:
            sql_script = file.read()
        logging.info(f"Successfully read SQL file: {file_path}")
        return sql_script
    except Exception as e:
        logging.error(f"Error reading sql file {file_path}: e")

def main():
    psql_conn = psql_engine()
    if psql_conn is None:
        logging.error("PSQL connection fail, exiting script.")
        return None
    try:
        # Read and execute drop statement
        drop_sql = read_sql_files("/mnt/c/users/rflores/poetry_env/KP_EthnicityGender/tableau_tables/unemployment/drop.sql")
        psql_conn.execute(text(drop_sql))
        logging.info("Executed drop SQL script successfully.")

        #Read and execute create table
        create_sql = read_sql_files("/mnt/c/users/rflores/poetry_env/KP_EthnicityGender/tableau_tables/unemployment/unemp.sql")
        psql_conn.execute(text(create_sql))
        logging.info("ran sql script. Yay!")
    finally:
        psql_conn.close()

if __name__ == "__main__":
    main()
