# Union Member Analytics & Reporting System

A data pipeline system that analyzes union member demographics, employment status, and work hours to generate reports and populate Tableau dashboards.

## Overview

This system queries union member data and creates analytical tables for:
- Member demographics by ethnicity and gender
- Employment/unemployment analysis
- Work hours tracking across locals
- Generation-based member analysis

## Components

### Tableau Tables
- **Ethnicity & Gender Analysis** (`tableau_tables/ethnicity_gender/`) - Creates `kpEthnicityAnalysis` table
- **Unemployment Analysis** (`tableau_tables/unemployment/`) - Creates `kp_unemployment` table

### Monthly Reports
Generates member reports filtered by ethnicity:
- African American (`afam_hours_12months/`)
- Asian (`ASN/`)
- Hispanic (`HSP/`) 
- White (`WHT/`)
- American Indian (`AMI/`)
- Unknown (`UNK/`)

## Data Sources
- `zzmember_base` - Member demographics and status
- `csac_all_funds2` - Work hours and employment data
- `dispatch` - Dispatch records

## Usage

Run individual components:
```bash
# Generate ethnicity/gender analysis
cd tableau_tables/ethnicity_gender
poetry run python3 ethandg.py

# Generate unemployment analysis  
cd tableau_tables/unemployment
poetry run python3 unemployment.py
```

## Automation
Bash scripts with cron scheduling available for automated execution.
