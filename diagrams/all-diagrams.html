<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Project Diagrams - SVG Export</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .diagram-container {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .diagram-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }
        .export-instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        .mermaid {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 5px;
        }
    </style>
</head>
<body>

<h1>Project Diagrams - SVG Export</h1>

<div class="export-instructions">
    <h3>📥 How to Export as SVG:</h3>
    <ol>
        <li><strong>Right-click</strong> on any diagram below</li>
        <li><strong>Select "Save image as..."</strong> or <strong>"Copy image"</strong></li>
        <li><strong>Choose SVG format</strong> when saving</li>
        <li><strong>Alternative:</strong> Copy the code from the .mmd files and use <a href="https://mermaid.live/" target="_blank">mermaid.live</a></li>
    </ol>
</div>

<div class="diagram-container">
    <div class="diagram-title">Current vs Future Architecture</div>
    <div class="mermaid">
graph TB
    subgraph "CURRENT STATE: Manual & Fragmented"
        A1[Python Script<br/>Asian Reports<br/>🔑 Hardcoded Passwords]
        A2[Python Script<br/>Hispanic Reports<br/>🔑 Hardcoded Passwords]
        A3[Python Script<br/>White Reports<br/>🔑 Hardcoded Passwords]
        A4[Python Script<br/>African American<br/>🔑 Hardcoded Passwords]
        A5[Python Script<br/>Unknown Reports<br/>🔑 Hardcoded Passwords]
        A6[Python Script<br/>American Indian<br/>🔑 Hardcoded Passwords]
        
        A1 --> DB1[(Database)]
        A2 --> DB1
        A3 --> DB1
        A4 --> DB1
        A5 --> DB1
        A6 --> DB1
        
        DB1 --> EMAIL1[📧 Manual Email<br/>to Kyle Patterson]
    end
    
    subgraph "FUTURE STATE: Automated & Secure"
        B1[🔐 Tokenizer Service<br/>Encrypted Credentials<br/>Centralized Security]
        B2[📊 Report API Service<br/>Single Codebase<br/>All Ethnicities]
        B3[📈 Metrics Service<br/>Business Value Tracking<br/>Real-time Dashboards]
        
        B1 <--> B2
        B2 <--> B3
        B2 --> DB2[(Database)]
        
        B2 --> EMAIL2[📧 Automated Email<br/>to Kyle Patterson<br/>+ Dashboards]
        B3 --> DASH[📊 Executive Dashboard<br/>ROI Metrics<br/>Performance Data]
    end
    
    style A1 fill:#ffcccc
    style A2 fill:#ffcccc
    style A3 fill:#ffcccc
    style A4 fill:#ffcccc
    style A5 fill:#ffcccc
    style A6 fill:#ffcccc
    style EMAIL1 fill:#ffcccc
    
    style B1 fill:#ccffcc
    style B2 fill:#ccffcc
    style B3 fill:#ccffcc
    style EMAIL2 fill:#ccffcc
    style DASH fill:#ccffcc
    </div>
</div>

<div class="diagram-container">
    <div class="diagram-title">Project Timeline & Value Creation</div>
    <div class="mermaid">
gantt
    title Project Timeline & Value Creation
    dateFormat  YYYY-MM-DD
    section Phase 1: Foundation
    Tokenizer Service           :active, p1a, 2025-01-13, 30d
    Convert First Report        :p1b, after p1a, 15d
    Basic Metrics              :p1c, after p1b, 15d
    section Phase 2: Expansion
    Migrate All Reports        :p2a, after p1c, 30d
    Advanced Metrics           :p2b, after p2a, 15d
    Automated Delivery         :p2c, after p2b, 15d
    section Phase 3: Optimization
    Executive Dashboard        :p3a, after p2c, 20d
    Performance Tuning         :p3b, after p3a, 20d
    Documentation             :p3c, after p3b, 20d
    section Value Milestones
    First Time Savings        :milestone, m1, after p1b, 0d
    50% Cost Reduction        :milestone, m2, after p2a, 0d
    Full ROI Achievement      :milestone, m3, after p3a, 0d
    </div>
</div>

<div class="diagram-container">
    <div class="diagram-title">Data Flow & Processing</div>
    <div class="mermaid">
flowchart TD
    START([Weekly Report Trigger]) --> AUTH[🔐 Get Credentials<br/>from Tokenizer Service]
    AUTH --> QUERY[📊 Query Database<br/>for Member Records]
    QUERY --> PROCESS[⚙️ Process Data<br/>by Ethnicity Groups]
    
    PROCESS --> ASIAN[👥 Asian Members<br/>Generate Report]
    PROCESS --> HISPANIC[👥 Hispanic Members<br/>Generate Report]
    PROCESS --> WHITE[👥 White Members<br/>Generate Report]
    PROCESS --> AFRICAN[👥 African American<br/>Generate Report]
    PROCESS --> UNKNOWN[👥 Unknown Ethnicity<br/>Generate Report]
    PROCESS --> NATIVE[👥 American Indian<br/>Generate Report]
    
    ASIAN --> COMBINE[📋 Combine All Reports]
    HISPANIC --> COMBINE
    WHITE --> COMBINE
    AFRICAN --> COMBINE
    UNKNOWN --> COMBINE
    NATIVE --> COMBINE
    
    COMBINE --> EMAIL[📧 Send to Kyle Patterson]
    COMBINE --> METRICS[📈 Record Metrics]
    
    METRICS --> DASHBOARD[📊 Update Dashboard]
    EMAIL --> COMPLETE([✅ Process Complete])
    DASHBOARD --> COMPLETE
    
    style START fill:#e1f5fe
    style AUTH fill:#fff3e0
    style QUERY fill:#f3e5f5
    style PROCESS fill:#e8f5e8
    style COMBINE fill:#fff8e1
    style EMAIL fill:#e3f2fd
    style METRICS fill:#f1f8e9
    style DASHBOARD fill:#fce4ec
    style COMPLETE fill:#e8f5e8
    </div>
</div>

<script>
    mermaid.initialize({ 
        startOnLoad: true,
        theme: 'default',
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true
        }
    });
</script>

</body>
</html>
