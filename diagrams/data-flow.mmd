flowchart TD
    START([Weekly Report Trigger]) --> AUTH[🔐 Get Credentials<br/>from Tokenizer Service]
    AUTH --> QUERY[📊 Query Database<br/>for Member Records]
    QUERY --> PROCESS[⚙️ Process Data<br/>by Ethnicity Groups]
    
    PROCESS --> ASIAN[👥 Asian Members<br/>Generate Report]
    PROCESS --> HISPANIC[👥 Hispanic Members<br/>Generate Report]
    PROCESS --> WHITE[👥 White Members<br/>Generate Report]
    PROCESS --> AFRICAN[👥 African American<br/>Generate Report]
    PROCESS --> UNKNOWN[👥 Unknown Ethnicity<br/>Generate Report]
    PROCESS --> NATIVE[👥 American Indian<br/>Generate Report]
    
    ASIAN --> COMBINE[📋 Combine All Reports]
    HISPANIC --> COMBINE
    WHITE --> COMBINE
    AFRICAN --> COMBINE
    UNKNOWN --> COMBINE
    NATIVE --> COMBINE
    
    COMBINE --> EMAIL[📧 Send to <PERSON>]
    COMBINE --> METRICS[📈 Record Metrics]
    
    METRICS --> DASHBOARD[📊 Update Dashboard]
    EMAIL --> COMPLETE([✅ Process Complete])
    DASHBOARD --> COMPLETE
    
    style START fill:#e1f5fe
    style AUTH fill:#fff3e0
    style QUERY fill:#f3e5f5
    style PROCESS fill:#e8f5e8
    style COMBINE fill:#fff8e1
    style EMAIL fill:#e3f2fd
    style METRICS fill:#f1f8e9
    style DASHBOARD fill:#fce4ec
    style COMPLETE fill:#e8f5e8
