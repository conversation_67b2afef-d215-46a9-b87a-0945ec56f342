graph TB
    subgraph "CURRENT STATE: Manual & Fragmented"
        A1[Python Script<br/>Asian Reports<br/>🔑 Hardcoded Passwords]
        A2[Python Script<br/>Hispanic Reports<br/>🔑 Hardcoded Passwords]
        A3[Python Script<br/>White Reports<br/>🔑 Hardcoded Passwords]
        A4[Python Script<br/>African American<br/>🔑 Hardcoded Passwords]
        A5[Python Script<br/>Unknown Reports<br/>🔑 Hardcoded Passwords]
        A6[Python Script<br/>American Indian<br/>🔑 Hardcoded Passwords]
        
        A1 --> DB1[(Database)]
        A2 --> DB1
        A3 --> DB1
        A4 --> DB1
        A5 --> DB1
        A6 --> DB1
        
        DB1 --> EMAIL1[📧 Manual Email<br/>to <PERSON>]
    end
    
    subgraph "FUTURE STATE: Automated & Secure"
        B1[🔐 Tokenizer Service<br/>Encrypted Credentials<br/>Centralized Security]
        B2[📊 Report API Service<br/>Single Codebase<br/>All Ethnicities]
        B3[📈 Metrics Service<br/>Business Value Tracking<br/>Real-time Dashboards]
        
        B1 <--> B2
        B2 <--> B3
        B2 --> DB2[(Database)]
        
        B2 --> EMAIL2[📧 Automated Email<br/>to <PERSON><br/>+ Dashboards]
        B3 --> DASH[📊 Executive Dashboard<br/>ROI Metrics<br/>Performance Data]
    end
    
    style A1 fill:#ffcccc
    style A2 fill:#ffcccc
    style A3 fill:#ffcccc
    style A4 fill:#ffcccc
    style A5 fill:#ffcccc
    style A6 fill:#ffcccc
    style EMAIL1 fill:#ffcccc
    
    style B1 fill:#ccffcc
    style B2 fill:#ccffcc
    style B3 fill:#ccffcc
    style EMAIL2 fill:#ccffcc
    style DASH fill:#ccffcc
