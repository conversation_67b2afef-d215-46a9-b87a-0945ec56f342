# Week 1: Foundation & Quick Wins

## 🎯 **Week 1 Learning Goals**
**You will learn by building:**
- How to analyze and refactor legacy code
- Configuration management patterns in Python
- Database connection best practices
- Error handling and logging strategies
- Test-driven development basics

**You will build:** A clean, configurable foundation for your data pipeline

## 📅 **Daily Breakdown**

### **Day 1-2: Project Restructure & Configuration**
- Analyze current codebase issues
- Create unified configuration system
- Remove hardcoded paths
- Consolidate duplicate code patterns

### **Day 3-4: Database & Error Handling**
- Implement connection pooling
- Add retry logic and proper error handling
- Create reusable database utilities
- Improve logging system

### **Day 5: Testing & Documentation**
- Add unit tests
- Create API documentation
- Set up development environment
- Prepare for Week 2

## 🔍 **Current Issues Analysis**

### **Problems Identified:**
1. **Hardcoded Paths**: `/mnt/c/users/rflores/poetry_env/KP_EthnicityGender/`
2. **Code Duplication**: Same patterns in healthcare_qualy, tableau_tables, monthly_reports
3. **Poor Error Handling**: Limited try-catch blocks
4. **No Configuration Management**: Database credentials scattered
5. **Resource Leaks**: Database connections not properly managed

### **Impact Assessment:**
- **Maintenance**: Hard to update and deploy
- **Reliability**: Prone to failures
- **Scalability**: Cannot handle increased load
- **Security**: Credentials in code

## 🏗️ **New Architecture Design**

### **Directory Structure:**
```
src/
├── config/
│   ├── __init__.py
│   ├── database.py          # Database configuration
│   ├── settings.py          # Application settings
│   └── logging_config.py    # Logging setup
├── data_processing/
│   ├── __init__.py
│   ├── base_processor.py    # Base class for all processors
│   ├── healthcare_quality.py
│   ├── ethnicity_gender.py
│   └── unemployment.py
├── database/
│   ├── __init__.py
│   ├── connection_manager.py # Connection pooling
│   ├── query_executor.py    # SQL execution utilities
│   └── migrations/          # Database migrations
├── utils/
│   ├── __init__.py
│   ├── file_manager.py      # File operations
│   ├── validators.py        # Data validation
│   └── exceptions.py        # Custom exceptions
└── sql/
    ├── healthcare/
    ├── ethnicity_gender/
    └── unemployment/
```

## 🛠️ **Implementation Steps**

### **Step 1: Configuration Management**

Create a centralized configuration system:

```python
# src/config/settings.py
from pydantic import BaseSettings
from typing import Optional

class DatabaseSettings(BaseSettings):
    host: str
    port: int = 25060
    username: str
    password: str
    database: str = "defaultdb"
    
    class Config:
        env_prefix = "DB_"

class AppSettings(BaseSettings):
    environment: str = "development"
    log_level: str = "INFO"
    sql_files_path: str = "sql"
    
    database: DatabaseSettings = DatabaseSettings()
```

### **Step 2: Database Connection Manager**

Implement proper connection pooling:

```python
# src/database/connection_manager.py
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager

class DatabaseManager:
    def __init__(self, settings: DatabaseSettings):
        self.engine = create_engine(
            f"postgresql+psycopg2://{settings.username}:{settings.password}@{settings.host}:{settings.port}/{settings.database}",
            poolclass=QueuePool,
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True
        )
    
    @contextmanager
    def get_connection(self):
        conn = self.engine.connect()
        try:
            yield conn
        finally:
            conn.close()
```

### **Step 3: Base Processor Class**

Create a reusable base class:

```python
# src/data_processing/base_processor.py
from abc import ABC, abstractmethod
import logging
from pathlib import Path

class BaseDataProcessor(ABC):
    def __init__(self, name: str, db_manager: DatabaseManager):
        self.name = name
        self.db_manager = db_manager
        self.logger = logging.getLogger(f"processor.{name}")
    
    @abstractmethod
    def process(self) -> bool:
        """Main processing logic"""
        pass
    
    def read_sql_file(self, filename: str) -> str:
        """Read SQL file with proper path resolution"""
        sql_path = Path("sql") / self.name / filename
        return sql_path.read_text()
```

## 📋 **Daily Tasks**

### **Day 1: Setup & Analysis**
- [ ] Create new directory structure
- [ ] Analyze existing code patterns
- [ ] Identify configuration requirements
- [ ] Set up development environment

### **Day 2: Configuration System**
- [ ] Implement settings management
- [ ] Create environment variable system
- [ ] Add validation for configurations
- [ ] Test configuration loading

### **Day 3: Database Utilities**
- [ ] Create connection manager
- [ ] Implement query executor
- [ ] Add retry logic
- [ ] Test database operations

### **Day 4: Error Handling & Logging**
- [ ] Set up structured logging
- [ ] Add comprehensive error handling
- [ ] Create custom exceptions
- [ ] Implement monitoring hooks

### **Day 5: Testing & Documentation**
- [ ] Write unit tests
- [ ] Create integration tests
- [ ] Document APIs
- [ ] Prepare for containerization

## 🧪 **Testing Strategy**

### **Unit Tests:**
- Configuration loading
- Database connection management
- SQL file reading
- Data processing logic

### **Integration Tests:**
- End-to-end pipeline execution
- Database operations
- Error scenarios
- Performance benchmarks

## 📊 **Success Metrics**

By end of Week 1:
- [ ] Zero hardcoded paths
- [ ] 90%+ code coverage
- [ ] All tests passing
- [ ] Proper error handling
- [ ] Centralized configuration
- [ ] Improved logging

## 🔄 **Migration Strategy**

1. **Parallel Development**: Build new system alongside existing
2. **Gradual Migration**: Move one pipeline at a time
3. **Validation**: Compare outputs between old and new systems
4. **Rollback Plan**: Keep old system until new is proven

## 📚 **Learning Resources**

- [Pydantic Settings](https://pydantic-docs.helpmanual.io/usage/settings/)
- [SQLAlchemy Connection Pooling](https://docs.sqlalchemy.org/en/14/core/pooling.html)
- [Python Logging Best Practices](https://docs.python.org/3/howto/logging.html)
- [Pytest Testing Framework](https://docs.pytest.org/)

## ➡️ **Next Week Preview**

Week 2 will focus on:
- Docker containerization
- Multi-stage builds
- Docker Compose development environment
- Container health checks
- Local orchestration

---

**Ready to start? Let's begin with [Day 1 Implementation](week1-day1.md)!**
