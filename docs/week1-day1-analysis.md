# Day 1: Legacy Code Analysis & Planning

## 🎯 **Today's Learning Objectives**
- Analyze legacy code to identify patterns and problems
- Understand the business logic behind each pipeline
- Plan a modern architecture that preserves functionality
- Create a migration strategy

## 🔍 **Your Analysis Assignment**

### **Step 1: Code Archaeology (30 minutes)**

Examine <PERSON>'s code and answer these questions:

**Healthcare Quality Pipeline:**
1. What does `healthcare_qualy/healthcq.py` actually do? 
2. What business rules are implemented in the quota/bank logic?
3. What data does `hcqualified.sql` extract?
4. Why is there a hardcoded path on line 60?

**Ethnicity & Gender Pipeline:**
1. What tables does `eandg.sql` create?
2. What's the purpose of the `drop.sql` file?
3. How does the generation categorization work?
4. What's the relationship between `membase` and `csac` CTEs?

**Pattern Recognition:**
1. What code is duplicated across pipelines?
2. What configuration should be externalized?
3. What error scenarios are not handled?

### **Step 2: Business Logic Documentation (45 minutes)**

Create a document that explains:

**Healthcare Quality Business Rules:**
- What is the quota system? (quota = 120, bank_cap = 720)
- How does the banking logic work?
- What happens when hours >= quota vs hours < quota?
- What data goes to Tableau?

**Ethnicity & Gender Analysis:**
- What demographic analysis is being performed?
- What time periods are analyzed?
- Which locals/trusts are included?
- What generations are tracked and why?

### **Step 3: Problem Identification (30 minutes)**

List specific issues you found:

**Technical Debt:**
- [ ] Hardcoded paths (list them)
- [ ] Duplicate code (where?)
- [ ] Missing error handling (which functions?)
- [ ] Resource management issues (database connections?)

**Operational Issues:**
- [ ] Manual deployment process
- [ ] No monitoring/alerting
- [ ] No automated testing
- [ ] Configuration scattered across files

**Scalability Concerns:**
- [ ] Single-threaded processing
- [ ] No retry logic
- [ ] Fixed resource allocation
- [ ] Manual scheduling

## 🏗️ **Architecture Planning Exercise**

### **Step 4: Design Your Target Architecture (45 minutes)**

Draw (or describe) your ideal architecture:

**Questions to Consider:**
1. How would you organize the code to eliminate duplication?
2. Where should configuration be stored?
3. How would you handle database connections?
4. What would make this easier to test?
5. How could you make it more reliable?

**Create a simple diagram showing:**
- Data flow from database → processing → output
- Where configuration comes from
- How errors are handled
- Where logs go

### **Step 5: Migration Strategy (30 minutes)**

Plan how you'll migrate without breaking production:

**Phase 1 (This Week):**
- What can you refactor safely?
- How will you test equivalence?
- What's your rollback plan?

**Phase 2-4 (Future Weeks):**
- When will you containerize?
- How will you move to serverless?
- What's your testing strategy?

## 📝 **Deliverables for Today**

Create these files in your project:

1. **`analysis/legacy-code-review.md`** - Your findings from Step 1
2. **`analysis/business-logic.md`** - Documentation from Step 2  
3. **`analysis/technical-debt.md`** - Issues from Step 3
4. **`analysis/target-architecture.md`** - Your design from Step 4
5. **`analysis/migration-plan.md`** - Strategy from Step 5

## 🤔 **Questions to Ask Yourself**

- What would happen if the database connection failed?
- How would you know if the data processing was wrong?
- What if you needed to process 10x more data?
- How would a new team member understand this code?
- What if you needed to run this in the cloud?

## 💡 **Hints & Tips**

**For Code Analysis:**
- Use `grep -r "hardcoded_path" .` to find patterns
- Look for repeated imports and functions
- Check what happens in exception blocks (or lack thereof)

**For Business Logic:**
- The quota/banking system is like a time-off accrual system
- Generation categories follow standard demographic research
- Trust locals are probably union local numbers

**For Architecture:**
- Think about separation of concerns
- Consider what changes frequently vs rarely
- Plan for failure scenarios

## 🎯 **Success Criteria**

By end of day, you should:
- [ ] Understand what each pipeline does
- [ ] Have documented the business logic
- [ ] Identified specific technical problems
- [ ] Designed a better architecture
- [ ] Planned your migration approach

## ❓ **When to Ask for Help**

Ask me when you:
- Can't understand what a piece of code does
- Need clarification on database schema
- Want to validate your architecture ideas
- Get stuck on any analysis step

## ➡️ **Tomorrow Preview**

Day 2: You'll start building the configuration management system based on your analysis today.

---

**Ready to dive in? Start with the code archaeology and let me know what you discover!**
