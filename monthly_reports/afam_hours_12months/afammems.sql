WITH membase AS (
    SELECT 
        zzmember_base.first_name::text || ' ' || zzmember_base.last_name::text AS name,
        zzmember_base.local AS member_local,
        zzmember_base.ubcid,
        zzmember_base.status,
        zzmember_base.ethnicity,
        zzmember_base.gender,
        zzmember_base.craft,
        zzmember_base.class,
        zzmember_base.dob,
        zzmember_base.phone1,
        zzmember_base.email,
        CASE 
            WHEN dob BETWEEN '1965-01-01' AND '1980-12-31' THEN 'Gen X' 
            WHEN dob BETWEEN '1981-01-01' AND '1996-12-31' THEN 'Millennial' 
            WHEN dob BETWEEN '1997-01-01' AND '2012-12-31' THEN 'Gen Z' 
            WHEN dob >= '2013-01-01' THEN 'Gen Alpha' 
            ELSE 'Baby Boomer or Older' 
        END AS generations,
        zzmember_base.ssn 
    FROM zzmember_base 
    GROUP BY name, member_local, ubcid, status, ethnicity, gender, craft, class, dob, ssn, dob, phone1, email
), 
csac AS (
    SELECT 
        csac_all_funds2.trust_local, 
        csac_all_funds2.member_id, 
        csac_all_funds2.hours, 
        work_period 
    FROM csac_all_funds2 
    WHERE fund = 'DUES'::text 
        AND work_period BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '15 MONTH'), 'YYYYMM')::INTEGER 
                         AND TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'), 'YYYYMM')::INTEGER 
        AND trust_local IN ('1977', '1912','661', '503', '323', '213', '206') 
        AND hours >= 0
), 
dispatch AS (
    SELECT 
        ubcid, 
        local_owl 
    FROM owlery 
    WHERE date = (SELECT MAX(date) FROM owlery)
) 
SELECT DISTINCT 
    membase.ubcid, 
    name, 
    status, 
    class, 
    craft, 
    member_local, 
    ethnicity, 
    gender, 
    generations, 
    dob, 
    SUM(hours), 
    phone1, 
    email, 
    CASE 
        WHEN dispatch.ubcid IS NOT NULL THEN 1 
        ELSE 0 
    END 
FROM membase 
FULL JOIN csac ON membase.ssn::text = csac.member_id::text 
LEFT JOIN dispatch ON dispatch.ubcid = membase.ubcid 
WHERE member_local IN ('1977', '1912','661', '503', '323', '213', '206') 
    AND status IN ('GS', 'AR') 
    AND ethnicity = 'BLK' 
GROUP BY membase.ubcid, name, member_local, status, generations, class, craft, ethnicity, gender, dob, phone1, email, dispatch.ubcid 
ORDER BY membase.ubcid DESC, member_local DESC;

