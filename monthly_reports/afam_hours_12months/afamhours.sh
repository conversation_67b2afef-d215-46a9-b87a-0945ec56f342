#!/bin/bash

# Exit script immediatly on error
set -e

LOGFILE="$(dirname "$0")/afamcron.log"
exec > >(tee -a "$LOGFILE") 2>&1

# print timestamp
echo "----- <PERSON><PERSON><PERSON> started at $(date) -----"

# path for poetry
export PATH="/home/<USER>/.local/bin:$PATH"

# Change to the script directory
cd  /mnt/c/users/rflores/poetry_env/KP_EthnicityGender/monthly_reports/afam_hours_12months

#hard coded poetry path into below command
/root/.local/bin/poetry run python3 afam_report.py

# Print success message
echo "<PERSON><PERSON><PERSON> completed successfully at $(date)"
