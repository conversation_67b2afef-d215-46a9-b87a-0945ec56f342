[tool.poetry]
name = "afam_hours_12months"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
requests = "^2.32.3"
pandas = "^2.2.2"
sqlalchemy = "^2.0.32"
psycopg2-binary = "^2.9.9"
mysql-connector-python = "^9.0.0"
schedule = "^1.2.2"
logging = "^*******"
python-dotenv = "^1.0.1"

[build-system]
requires = ["poetry-core"]
