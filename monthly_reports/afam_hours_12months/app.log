2025-02-06 13:29:18,910 - INFO - Data loaded from PSQL successfully.
2025-02-06 14:35:31,337 - INFO - I'm in main now!!!!
2025-02-06 14:35:34,216 - INFO - Data loaded from PSQL successfully.
2025-02-06 15:19:22,133 - INFO - I'm in main now!!!!
2025-02-06 15:19:24,783 - INFO - Data loaded from PSQL successfully.
2025-02-07 14:09:24,533 - INFO - I'm in main now!!!!
2025-02-07 14:09:24,534 - INFO - trying to connect to psql
2025-02-07 14:09:25,079 - INFO - trying to load from psql
2025-02-07 14:09:25,089 - INFO - trying to connect to psql
2025-02-07 14:09:28,083 - INFO - Data loaded from PSQL successfully.
2025-02-18 14:24:31,680 - INFO - I'm in main now!!!!
2025-02-18 14:24:31,682 - INFO - trying to connect to psql
2025-02-18 14:24:32,229 - INFO - trying to load from psql
2025-02-18 14:24:32,244 - INFO - trying to connect to psql
2025-02-18 14:24:35,197 - INFO - Data loaded from PSQL successfully.
2025-02-18 15:39:07,191 - INFO - I'm in main now!!!!
2025-02-18 15:39:07,192 - INFO - trying to connect to psql
2025-02-18 15:39:07,721 - INFO - trying to load from psql
2025-02-18 15:39:07,730 - INFO - trying to connect to psql
2025-02-19 09:26:30,974 - INFO - I'm in main now!!!!
2025-02-19 09:26:30,985 - INFO - trying to connect to psql
2025-02-19 09:26:31,493 - INFO - trying to load from psql
2025-02-19 09:39:44,863 - INFO - I'm in main now!!!!
2025-02-19 09:39:44,864 - INFO - trying to connect to psql
2025-02-19 09:39:45,279 - INFO - trying to load from psql
2025-02-19 09:40:34,767 - INFO - trying to connect to psql
2025-02-19 09:40:35,219 - INFO - trying to load from psql
2025-02-19 09:41:33,479 - INFO - trying to connect to psql
2025-02-19 09:59:23,267 - INFO - trying to connect to psql
2025-02-19 09:59:23,722 - INFO - trying to load from psql
2025-02-19 10:00:36,400 - INFO - trying to connect to psql
2025-02-19 10:00:36,820 - INFO - trying to load from psql
2025-02-19 10:01:37,266 - INFO - trying to connect to psql
2025-02-19 10:09:06,559 - INFO - trying to connect to psql
2025-02-19 10:09:19,329 - INFO - trying to connect to psql
2025-02-19 10:16:39,742 - INFO - I'm in main now!!!!
2025-02-19 10:16:39,742 - INFO - trying to connect to psql
2025-02-19 10:16:41,020 - INFO - We are now trying to convert SQL to DF
2025-02-19 13:38:21,639 - INFO - I'm in main now!!!!
2025-02-19 13:38:21,641 - INFO - trying to connect to psql
2025-02-19 13:38:23,634 - INFO - We are now trying to convert SQL to DF
2025-02-19 13:38:23,658 - INFO - connecting to gmail and send email
2025-02-20 09:51:28,532 - INFO - I'm in main now!!!!
2025-02-20 09:51:28,533 - INFO - trying to connect to psql
2025-02-20 09:51:32,700 - INFO - We are now trying to convert SQL to DF
2025-02-20 09:51:32,727 - INFO - connecting to gmail and send email
2025-03-03 08:33:01,706 - INFO - I'm in main now!!!!
2025-03-03 08:33:01,707 - INFO - trying to connect to psql
2025-03-03 08:33:04,962 - INFO - We are now trying to convert SQL to DF
2025-03-03 08:33:04,975 - INFO - connecting to gmail and send email
2025-03-17 12:42:12,361 - INFO - I'm in main now!!!!
2025-03-17 12:42:12,362 - INFO - trying to connect to psql
2025-03-17 12:42:16,263 - INFO - We are now trying to convert SQL to DF
2025-03-17 12:42:16,280 - INFO - connecting to outlook and sending email
2025-03-17 12:42:16,446 - INFO - Connected to SMTP server successfully.
2025-03-17 12:42:16,754 - INFO - TLS encryption started.
2025-03-17 12:48:34,729 - INFO - I'm in main now!!!!
2025-03-17 12:48:34,729 - INFO - trying to connect to psql
2025-03-17 12:48:36,108 - INFO - We are now trying to convert SQL to DF
2025-03-17 12:48:36,122 - INFO - connecting to outlook and sending email
2025-03-17 12:48:36,293 - INFO - Connected to SMTP server successfully.
2025-03-17 12:48:36,596 - INFO - TLS encryption started.
2025-03-17 14:06:11,291 - INFO - I'm in main now!!!!
2025-03-17 14:06:11,291 - INFO - trying to connect to psql
2025-03-17 14:06:12,934 - INFO - We are now trying to convert SQL to DF
2025-03-17 14:06:12,951 - INFO - connecting to outlook and sending email
2025-03-17 14:06:13,125 - INFO - Connected to SMTP server successfully.
2025-03-17 14:06:13,424 - INFO - TLS encryption started.
2025-03-17 14:07:06,602 - INFO - I'm in main now!!!!
2025-03-17 14:07:06,603 - INFO - trying to connect to psql
2025-03-17 14:07:08,164 - INFO - We are now trying to convert SQL to DF
2025-03-17 14:07:08,178 - INFO - connecting to outlook and sending email
2025-03-17 14:07:08,348 - INFO - Connected to SMTP server successfully.
2025-03-17 14:07:08,652 - INFO - TLS encryption started.
2025-03-17 14:08:49,536 - INFO - I'm in main now!!!!
2025-03-17 14:08:49,537 - INFO - trying to connect to psql
2025-03-17 14:08:51,276 - INFO - We are now trying to convert SQL to DF
2025-03-17 14:08:51,292 - INFO - connecting to outlook and sending email
2025-03-17 14:08:51,539 - INFO - Connected to SMTP server successfully.
2025-03-17 14:08:51,821 - INFO - TLS encryption started.
2025-03-17 14:23:30,229 - INFO - I'm in main now!!!!
2025-03-17 14:23:30,230 - INFO - trying to connect to psql
2025-03-17 14:23:32,174 - INFO - We are now trying to convert SQL to DF
2025-03-17 14:23:32,188 - INFO - connecting to outlook and sending email
2025-03-17 14:23:32,362 - INFO - Connected to SMTP server successfully.
2025-03-17 14:23:32,661 - INFO - TLS encryption started.
2025-03-17 14:23:33,373 - INFO - logged in succesfully
2025-03-17 14:23:34,971 - INFO - Email sent succesfully to recipients
2025-03-17 14:23:35,047 - INFO - SMTP session closed
2025-04-01 08:29:56,497 - INFO - I'm in main now!!!!
2025-04-01 08:29:56,497 - INFO - trying to connect to psql
2025-04-01 08:30:07,123 - INFO - We are now trying to convert SQL to DF
2025-04-01 08:30:07,147 - INFO - connecting to outlook and sending email
2025-04-01 08:30:07,343 - INFO - Connected to SMTP server successfully.
2025-04-01 08:30:07,761 - INFO - TLS encryption started.
2025-04-01 08:30:08,732 - INFO - logged in succesfully
2025-04-01 08:30:13,407 - INFO - Email sent succesfully to recipients
2025-04-01 08:30:13,483 - INFO - SMTP session closed
2025-04-02 09:34:29,467 - INFO - I'm in main now!!!!
2025-04-02 09:34:29,468 - INFO - trying to connect to psql
2025-04-02 09:34:30,096 - ERROR - error during data import ioload and transform: (psycopg2.errors.SyntaxError) syntax error at or near ","
LINE 37: ), 
          ^

[SQL: WITH membase AS (
    SELECT 
        zzmember_base.first_name::text || ' ' || zzmember_base.last_name::text AS name,
        zzmember_base.local AS member_local,
        zzmember_base.ubcid,
        zzmember_base.status,
        zzmember_base.ethnicity,
        zzmember_base.gender,
        zzmember_base.craft,
        zzmember_base.class,
        zzmember_base.dob,
        zzmember_base.phone1,
        zzmember_base.email,
        CASE 
            WHEN dob BETWEEN '1965-01-01' AND '1980-12-31' THEN 'Gen X' 
            WHEN dob BETWEEN '1981-01-01' AND '1996-12-31' THEN 'Millennial' 
            WHEN dob BETWEEN '1997-01-01' AND '2012-12-31' THEN 'Gen Z' 
            WHEN dob >= '2013-01-01' THEN 'Gen Alpha' 
            ELSE 'Baby Boomer or Older' 
        END AS generations,
        zzmember_base.ssn 
    FROM zzmember_base 
    GROUP BY name, member_local, ubcid, status, ethnicity, gender, craft, class, dob, ssn, dob, phone1, email
), 
csac AS (
    SELECT 
        csac_all_funds2.trust_local, 
        csac_all_funds2.member_id, 
        csac_all_funds2.hours, 
        work_period 
    FROM csac_all_funds2 
    WHERE fund = 'DUES'::text 
        AND work_period BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '15 MONTH'), 'YYYYMM')::INTEGER 
                         AND TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'), 'YYYYMM')::INTEGER 
        AND trust_local IN (('1977', '1912','661', '503', '323', '213', '206') 
        AND hours >= 0
), 
dispatch AS (
    SELECT 
        ubcid, 
        local_owl 
    FROM owlery 
    WHERE date = (SELECT MAX(date) FROM owlery)
) 
SELECT DISTINCT 
    membase.ubcid, 
    name, 
    status, 
    class, 
    craft, 
    member_local, 
    ethnicity, 
    gender, 
    generations, 
    dob, 
    SUM(hours), 
    phone1, 
    email, 
    CASE 
        WHEN dispatch.ubcid IS NOT NULL THEN 1 
        ELSE 0 
    END 
FROM membase 
FULL JOIN csac ON membase.ssn::text = csac.member_id::text 
LEFT JOIN dispatch ON dispatch.ubcid = membase.ubcid 
WHERE member_local IN ('1977', '1912','661', '503', '323', '213', '206') 
    AND status IN ('GS', 'AR') 
    AND ethnicity = 'BLK' 
GROUP BY membase.ubcid, name, member_local, status, generations, class, craft, ethnicity, gender, dob, phone1, email, dispatch.ubcid 
ORDER BY membase.ubcid DESC, member_local DESC;

]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-04-02 09:52:25,852 - INFO - I'm in main now!!!!
2025-04-02 09:52:25,852 - INFO - trying to connect to psql
2025-04-02 09:52:26,469 - ERROR - error during data import ioload and transform: (psycopg2.errors.SyntaxError) syntax error at or near ","
LINE 37: ), 
          ^

[SQL: WITH membase AS (
    SELECT 
        zzmember_base.first_name::text || ' ' || zzmember_base.last_name::text AS name,
        zzmember_base.local AS member_local,
        zzmember_base.ubcid,
        zzmember_base.status,
        zzmember_base.ethnicity,
        zzmember_base.gender,
        zzmember_base.craft,
        zzmember_base.class,
        zzmember_base.dob,
        zzmember_base.phone1,
        zzmember_base.email,
        CASE 
            WHEN dob BETWEEN '1965-01-01' AND '1980-12-31' THEN 'Gen X' 
            WHEN dob BETWEEN '1981-01-01' AND '1996-12-31' THEN 'Millennial' 
            WHEN dob BETWEEN '1997-01-01' AND '2012-12-31' THEN 'Gen Z' 
            WHEN dob >= '2013-01-01' THEN 'Gen Alpha' 
            ELSE 'Baby Boomer or Older' 
        END AS generations,
        zzmember_base.ssn 
    FROM zzmember_base 
    GROUP BY name, member_local, ubcid, status, ethnicity, gender, craft, class, dob, ssn, dob, phone1, email
), 
csac AS (
    SELECT 
        csac_all_funds2.trust_local, 
        csac_all_funds2.member_id, 
        csac_all_funds2.hours, 
        work_period 
    FROM csac_all_funds2 
    WHERE fund = 'DUES'::text 
        AND work_period BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '15 MONTH'), 'YYYYMM')::INTEGER 
                         AND TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'), 'YYYYMM')::INTEGER 
        AND trust_local IN (('1977', '1912','661', '503', '323', '213', '206') 
        AND hours >= 0
), 
dispatch AS (
    SELECT 
        ubcid, 
        local_owl 
    FROM owlery 
    WHERE date = (SELECT MAX(date) FROM owlery)
) 
SELECT DISTINCT 
    membase.ubcid, 
    name, 
    status, 
    class, 
    craft, 
    member_local, 
    ethnicity, 
    gender, 
    generations, 
    dob, 
    SUM(hours), 
    phone1, 
    email, 
    CASE 
        WHEN dispatch.ubcid IS NOT NULL THEN 1 
        ELSE 0 
    END 
FROM membase 
FULL JOIN csac ON membase.ssn::text = csac.member_id::text 
LEFT JOIN dispatch ON dispatch.ubcid = membase.ubcid 
WHERE member_local IN ('1977', '1912','661', '503', '323', '213', '206') 
    AND status IN ('GS', 'AR') 
    AND ethnicity = 'BLK' 
GROUP BY membase.ubcid, name, member_local, status, generations, class, craft, ethnicity, gender, dob, phone1, email, dispatch.ubcid 
ORDER BY membase.ubcid DESC, member_local DESC;

]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-04-02 09:53:30,952 - INFO - I'm in main now!!!!
2025-04-02 09:53:30,953 - INFO - trying to connect to psql
2025-04-02 09:53:31,522 - ERROR - error during data import ioload and transform: (psycopg2.errors.SyntaxError) syntax error at or near ","
LINE 37: ), 
          ^

[SQL: WITH membase AS (
    SELECT 
        zzmember_base.first_name::text || ' ' || zzmember_base.last_name::text AS name,
        zzmember_base.local AS member_local,
        zzmember_base.ubcid,
        zzmember_base.status,
        zzmember_base.ethnicity,
        zzmember_base.gender,
        zzmember_base.craft,
        zzmember_base.class,
        zzmember_base.dob,
        zzmember_base.phone1,
        zzmember_base.email,
        CASE 
            WHEN dob BETWEEN '1965-01-01' AND '1980-12-31' THEN 'Gen X' 
            WHEN dob BETWEEN '1981-01-01' AND '1996-12-31' THEN 'Millennial' 
            WHEN dob BETWEEN '1997-01-01' AND '2012-12-31' THEN 'Gen Z' 
            WHEN dob >= '2013-01-01' THEN 'Gen Alpha' 
            ELSE 'Baby Boomer or Older' 
        END AS generations,
        zzmember_base.ssn 
    FROM zzmember_base 
    GROUP BY name, member_local, ubcid, status, ethnicity, gender, craft, class, dob, ssn, dob, phone1, email
), 
csac AS (
    SELECT 
        csac_all_funds2.trust_local, 
        csac_all_funds2.member_id, 
        csac_all_funds2.hours, 
        work_period 
    FROM csac_all_funds2 
    WHERE fund = 'DUES'::text 
        AND work_period BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '15 MONTH'), 'YYYYMM')::INTEGER 
                         AND TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'), 'YYYYMM')::INTEGER 
        AND trust_local IN (('1977', '1912','661', '503', '323', '213', '206') 
        AND hours >= 0
), 
dispatch AS (
    SELECT 
        ubcid, 
        local_owl 
    FROM owlery 
    WHERE date = (SELECT MAX(date) FROM owlery)
) 
SELECT DISTINCT 
    membase.ubcid, 
    name, 
    status, 
    class, 
    craft, 
    member_local, 
    ethnicity, 
    gender, 
    generations, 
    dob, 
    SUM(hours), 
    phone1, 
    email, 
    CASE 
        WHEN dispatch.ubcid IS NOT NULL THEN 1 
        ELSE 0 
    END 
FROM membase 
FULL JOIN csac ON membase.ssn::text = csac.member_id::text 
LEFT JOIN dispatch ON dispatch.ubcid = membase.ubcid 
WHERE member_local IN ('1977', '1912','661', '503', '323', '213', '206') 
    AND status IN ('GS', 'AR') 
    AND ethnicity = 'BLK' 
GROUP BY membase.ubcid, name, member_local, status, generations, class, craft, ethnicity, gender, dob, phone1, email, dispatch.ubcid 
ORDER BY membase.ubcid DESC, member_local DESC;

]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-04-02 09:54:08,084 - INFO - I'm in main now!!!!
2025-04-02 09:54:08,085 - INFO - trying to connect to psql
2025-04-02 09:54:08,672 - ERROR - error during data import ioload and transform: (psycopg2.errors.SyntaxError) syntax error at or near ","
LINE 37: ), 
          ^

[SQL: WITH membase AS (
    SELECT 
        zzmember_base.first_name::text || ' ' || zzmember_base.last_name::text AS name,
        zzmember_base.local AS member_local,
        zzmember_base.ubcid,
        zzmember_base.status,
        zzmember_base.ethnicity,
        zzmember_base.gender,
        zzmember_base.craft,
        zzmember_base.class,
        zzmember_base.dob,
        zzmember_base.phone1,
        zzmember_base.email,
        CASE 
            WHEN dob BETWEEN '1965-01-01' AND '1980-12-31' THEN 'Gen X' 
            WHEN dob BETWEEN '1981-01-01' AND '1996-12-31' THEN 'Millennial' 
            WHEN dob BETWEEN '1997-01-01' AND '2012-12-31' THEN 'Gen Z' 
            WHEN dob >= '2013-01-01' THEN 'Gen Alpha' 
            ELSE 'Baby Boomer or Older' 
        END AS generations,
        zzmember_base.ssn 
    FROM zzmember_base 
    GROUP BY name, member_local, ubcid, status, ethnicity, gender, craft, class, dob, ssn, dob, phone1, email
), 
csac AS (
    SELECT 
        csac_all_funds2.trust_local, 
        csac_all_funds2.member_id, 
        csac_all_funds2.hours, 
        work_period 
    FROM csac_all_funds2 
    WHERE fund = 'DUES'::text 
        AND work_period BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '15 MONTH'), 'YYYYMM')::INTEGER 
                         AND TO_CHAR((CURRENT_DATE - INTERVAL '3 MONTH'), 'YYYYMM')::INTEGER 
        AND trust_local IN (('1977', '1912','661', '503', '323', '213', '206') 
        AND hours >= 0
), 
dispatch AS (
    SELECT 
        ubcid, 
        local_owl 
    FROM owlery 
    WHERE date = (SELECT MAX(date) FROM owlery)
) 
SELECT DISTINCT 
    membase.ubcid, 
    name, 
    status, 
    class, 
    craft, 
    member_local, 
    ethnicity, 
    gender, 
    generations, 
    dob, 
    SUM(hours), 
    phone1, 
    email, 
    CASE 
        WHEN dispatch.ubcid IS NOT NULL THEN 1 
        ELSE 0 
    END 
FROM membase 
FULL JOIN csac ON membase.ssn::text = csac.member_id::text 
LEFT JOIN dispatch ON dispatch.ubcid = membase.ubcid 
WHERE member_local IN ('1977', '1912','661', '503', '323', '213', '206') 
    AND status IN ('GS', 'AR') 
    AND ethnicity = 'BLK' 
GROUP BY membase.ubcid, name, member_local, status, generations, class, craft, ethnicity, gender, dob, phone1, email, dispatch.ubcid 
ORDER BY membase.ubcid DESC, member_local DESC;

]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-04-02 09:55:50,916 - INFO - I'm in main now!!!!
2025-04-02 09:55:50,916 - INFO - trying to connect to psql
2025-04-02 09:55:53,488 - INFO - We are now trying to convert SQL to DF
2025-04-02 09:55:53,506 - INFO - connecting to outlook and sending email
2025-04-02 09:55:53,670 - INFO - Connected to SMTP server successfully.
2025-04-02 09:55:53,951 - INFO - TLS encryption started.
2025-04-02 09:55:55,273 - INFO - logged in succesfully
2025-04-02 09:55:57,123 - INFO - Email sent succesfully to recipients
2025-04-02 09:55:57,194 - INFO - SMTP session closed
2025-05-01 08:25:09,835 - INFO - I'm in main now!!!!
2025-05-01 08:25:09,836 - INFO - trying to connect to psql
2025-05-01 08:25:12,285 - INFO - We are now trying to convert SQL to DF
2025-05-01 08:25:12,304 - INFO - connecting to outlook and sending email
2025-05-01 08:25:12,565 - INFO - Connected to SMTP server successfully.
2025-05-01 08:25:12,867 - INFO - TLS encryption started.
2025-05-01 08:25:13,524 - INFO - logged in succesfully
2025-05-01 08:25:15,025 - INFO - Email sent succesfully to recipients
2025-05-01 08:25:15,102 - INFO - SMTP session closed
2025-06-03 08:41:33,945 - INFO - I'm in main now!!!!
2025-06-03 08:41:33,946 - INFO - trying to connect to psql
2025-06-03 08:41:38,542 - INFO - We are now trying to convert SQL to DF
2025-06-03 08:41:38,563 - INFO - connecting to outlook and sending email
2025-06-03 08:41:38,754 - INFO - Connected to SMTP server successfully.
2025-06-03 08:41:39,076 - INFO - TLS encryption started.
2025-06-03 08:41:39,819 - INFO - logged in succesfully
2025-06-03 08:41:41,731 - INFO - Email sent succesfully to recipients
2025-06-03 08:41:41,811 - INFO - SMTP session closed
2025-06-23 13:28:27,256 - INFO - I'm in main now!!!!
2025-06-23 13:28:27,256 - INFO - trying to connect to psql
2025-06-23 13:28:31,617 - INFO - We are now trying to convert SQL to DF
2025-06-23 13:28:31,632 - INFO - connecting to outlook and sending email
2025-06-23 13:28:31,870 - INFO - Connected to SMTP server successfully.
2025-06-23 13:28:32,242 - INFO - TLS encryption started.
2025-06-23 13:28:33,584 - INFO - logged in succesfully
2025-06-23 13:28:35,879 - INFO - Email sent succesfully to recipients
2025-06-23 13:28:35,961 - INFO - SMTP session closed
2025-06-23 16:07:03,620 - INFO - I'm in main now!!!!
2025-06-23 16:07:03,620 - INFO - trying to connect to psql
2025-06-23 16:07:05,959 - INFO - We are now trying to convert SQL to DF
2025-06-23 16:07:05,989 - INFO - connecting to outlook and sending email
2025-06-23 16:07:06,255 - INFO - Connected to SMTP server successfully.
2025-06-23 16:07:06,600 - INFO - TLS encryption started.
2025-06-23 16:07:07,384 - INFO - logged in succesfully
2025-06-23 16:07:08,348 - INFO - Email sent succesfully to recipients
2025-06-23 16:07:08,425 - INFO - SMTP session closed
