#!/bin/bash
# Change to the script directory

# Exit script immediatly on error
set -e

LOGFILE="$(dirname "$0")/afamcron.log"
exec > >(tee -a "$LOGFILE") 2>&1

# print timestamp
echo "----- <PERSON><PERSON><PERSON> started at $(date) -----"

cd   /mnt/c/users/rflores/poetry_env/KP_EthnicityGender/monthly_reports/ASN

# /root/.cache/pypoetry/virtualenvs/ASN-jI3483wr-py3.12/bin/python asn_report.py

poetry run python3 asn_report.py

# Print success message
echo "<PERSON><PERSON><PERSON> completed successfully at $(date)"
