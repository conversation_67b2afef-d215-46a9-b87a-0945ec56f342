#!/bin/bash

# Exit script immediatly on error
set -e

LOGFILE="$(dirname "$0")/whtcron.log"
exec > >(tee -a "$LOGFILE") 2>&1

# print timestamp
echo "----- <PERSON><PERSON><PERSON> started at $(date) -----"

# Change to the script directory
cd   /mnt/c/users/rflores/poetry_env/KP_EthnicityGender/monthly_reports/WHT || exit

poetry run python3 wht_report.py
# /root/.cache/pypoetry/virtualenvs/WHT-jI3483wr-py3.12/bin/python wht_report.py

# Print success message
echo "<PERSON><PERSON><PERSON> completed successfullyt at $(date)"
