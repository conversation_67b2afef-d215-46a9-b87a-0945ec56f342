import requests
import time
import pandas as pd
import logging
import smtplib
import io
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from schedule import every, repeat, run_pending
from sqlalchemy import create_engine, Table, Column, Integer, String, MetaData
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
import os

"""
the purpose of this script is to pull data from existing sql tables and email that info to KP and Tywanna
    1. First goal is query sql
    2. transform query to csv
    3. email csv
"""

logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
    )

# Load environmental variables from a .env file
load_dotenv()
DB_USER = os.getenv("DB_USER")
DB_PASS = os.getenv("DB_PASS")
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT", "25060")
DB_NAME = os.getenv("DB_NAME", "defaultdb")

# Filter for a specific date range
# Get current year and month
current_date = datetime.now()

# Get the max period (3 months before the current month)
max_period_date = current_date - relativedelta(months=3)
max_period = max_period_date.strftime("%Y%m")  # Format as YYYYMM

# Get the min period (12 months before max period)
min_period_date = max_period_date - relativedelta(months=12)
min_period = min_period_date.strftime("%Y%m")  # Format as YYYYMM

# Email Setup

sender_email = "<EMAIL>"
receiver_emails = ["<EMAIL>"]#, "<EMAIL>", "<EMAIL>"]  # List of recipients
subject = "Hours Worked for White Members in Last 12 Months"
body = "Hello, please see the attached report for a list of hours worked by white members in the previous 12-month period "+min_period +" to "+max_period+". Automated email."


# psql connection function
def psql_engine(): ####TESTED: PASS####
    try:
        logging.info("trying to connect to psql")
        pg_engine = create_engine(f'postgresql+psycopg2://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}') 
        psql_conn = pg_engine.connect()
        return psql_conn
    except Exception as e:
        logging.error(f"error during psql connection process: {e}")
        return None
# 
def read_sql_files(file_path):
    with open(file_path, "r") as file:
        return file.read()

#load data from psql and transform
def load_and_transform():
    psql_conn = psql_engine()
    if psql_conn is None:
        logging.error("PSQL connection failed, terminating the process.")
        return None
    try:
        #load data from mysql
        sql_query = read_sql_files("whtreport.sql")
        df = pd.read_sql_query(sql_query, psql_conn)
        return df
    except Exception as e:
        logging.error(f"error during data import ioload and transform: {e}")
        return None
    finally:
        psql_conn.close()

#Main function to run the script
def main():
    logging.info("I'm in main now!!!!")
    df = load_and_transform()
    if df is not None:
        print(df.head())

        # Convert DataFrame to CSV in memory
        csv_buffer = io.StringIO()
        try:
            logging.info("We are now trying to convert SQL to DF")
            df.to_csv(csv_buffer, index=False)
            csv_buffer.seek(0)
        except Exception as e:
            logging.error(f"error during dataframe conversion to csv in memory")

        # Email with attachment
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = ", ".join(receiver_emails)  # Join list of recipients
        message["Subject"] = subject
        message.attach(MIMEText(body, "plain"))

        # Add CSV as an attachment
        attachment = MIMEBase("application", "octet-stream")
        attachment.set_payload(csv_buffer.getvalue().encode())
        encoders.encode_base64(attachment)
        attachment.add_header("Content-Disposition", "attachment; filename=whtmember_report_"+max_period+".csv")
        message.attach(attachment)

        try:
            logging.info("connecting to outlook and sending email")
            server=smtplib.SMTP("us-smtp-outbound-1.mimecast.com", 587)  # Replace with your SMTP server
            server.set_debuglevel(1)
            logging.info("Connected to SMTP server successfully.")
            try:
                server.starttls()
                logging.info("TLS encryption started.")
            except smtplib.SMTPException:
                logging.warning("TLS not required or failed to start.")

            #login
            server.login("<EMAIL>","9vpIWI2zl8cO")
            logging.info("logged in succesfully")

            server.sendmail(sender_email, receiver_emails, message.as_string())
            logging.info("Email sent succesfully to recipients")

            #Close connection
            server.quit()
            logging.info("SMTP session closed")

            print("Email sent successfully!")
        except Exception as e:
            print(f"Error sending email: {e}")


if __name__ == "__main__":
    main()

