#!/bin/bash

# Exit script immediatly on error
set -e

LOGFILE="$(dirname "$0")/afamcron.log"
exec > >(tee -a "$LOGFILE") 2>&1

# print timestamp
echo "----- <PERSON><PERSON><PERSON> started at $(date) -----"

# Change to the script directory
cd   /mnt/c/users/rflores/poetry_env/KP_EthnicityGender/monthly_reports/AMI || exit

poetry run python3 ami_report.py

# Print success message
echo "Script completed successfully at $(date)"
